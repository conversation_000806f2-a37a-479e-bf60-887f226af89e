<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Star Rating System Test</title>
    <link rel="stylesheet" href="css/index.css" />
    <style>
      body {
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
      }
      .test-case {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 8px;
        background: rgba(0, 212, 255, 0.05);
      }
      .test-input {
        font-weight: bold;
        color: var(--accent-color);
      }
      .test-result {
        margin-top: 10px;
      }
      .before-after {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 10px;
      }
      .before,
      .after {
        padding: 10px;
        border-radius: 5px;
      }
      .before {
        background: rgba(255, 0, 0, 0.1);
        border: 1px solid rgba(255, 0, 0, 0.3);
      }
      .after {
        background: rgba(0, 255, 0, 0.1);
        border: 1px solid rgba(0, 255, 0, 0.3);
      }
      h1,
      h2,
      h3 {
        color: var(--text-color);
      }
      .explanation {
        margin: 20px 0;
        padding: 15px;
        background: rgba(0, 212, 255, 0.1);
        border-radius: 8px;
        border-left: 4px solid var(--accent-color);
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Star Rating System - Fixed Implementation</h1>

      <div class="explanation">
        <h3>What was fixed:</h3>
        <ul>
          <li>
            <strong>Star Positioning Bug:</strong> Rating 4.5 now correctly
            shows 4 full stars + 1 half star (not 5 full + 1 half)
          </li>
          <li>
            <strong>Rating Normalization:</strong> Invalid ratings (35, 50, 22,
            4.2) are normalized to allowed values [0.5, 1, 1.5, 2, 2.5, 3, 3.5,
            4, 4.5, 5]
          </li>
          <li>
            <strong>Edge Cases:</strong> Values outside 0-5 range are properly
            handled
          </li>
          <li>
            <strong>Rounding Logic:</strong> Values round UP to nearest 0.5
            increment (4.2 → 4.5, 3.1 → 3.5)
          </li>
        </ul>
      </div>

      <h2>Test Cases</h2>

      <div class="test-case">
        <div class="test-input">Input: 4.5 (The original bug case)</div>
        <div class="test-result" id="test-4-5"></div>
        <div class="explanation">
          Should show 4 full stars + 1 half star = 5 total stars
        </div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 4.2 (Backend invalid value)</div>
        <div class="test-result" id="test-4-2"></div>
        <div class="explanation">
          Normalized to 4.5: 4 full stars + 1 half star
        </div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 3.1 (Backend invalid value)</div>
        <div class="test-result" id="test-3-1"></div>
        <div class="explanation">
          Normalized to 3.5: 3 full stars + 1 half star
        </div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 35 (Backend invalid value)</div>
        <div class="test-result" id="test-35"></div>
        <div class="explanation">Normalized to 5.0: 5 full stars</div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 50 (Backend invalid value)</div>
        <div class="test-result" id="test-50"></div>
        <div class="explanation">Normalized to 5.0: 5 full stars</div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 22 (Backend invalid value)</div>
        <div class="test-result" id="test-22"></div>
        <div class="explanation">Normalized to 5.0: 5 full stars</div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 0 (Edge case)</div>
        <div class="test-result" id="test-0"></div>
        <div class="explanation">
          Normalized to 0.5: 0 full stars + 1 half star
        </div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: -5 (Edge case)</div>
        <div class="test-result" id="test-negative"></div>
        <div class="explanation">
          Normalized to 0.5: 0 full stars + 1 half star
        </div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 2.7 (Rounds up to 3.0)</div>
        <div class="test-result" id="test-2-7"></div>
        <div class="explanation">Normalized to 3.0: 3 full stars</div>
      </div>

      <div class="test-case">
        <div class="test-input">Input: 1.1 (Rounds up to 1.5)</div>
        <div class="test-result" id="test-1-1"></div>
        <div class="explanation">
          Normalized to 1.5: 1 full star + 1 half star
        </div>
      </div>

      <h2>Console Test</h2>
      <div class="explanation">
        <p>
          Open browser console and run <code>testStarRating()</code> to see
          detailed test results.
        </p>
        <button
          onclick="testStarRating()"
          style="
            padding: 10px 20px;
            background: var(--accent-color);
            color: black;
            border: none;
            border-radius: 5px;
            cursor: pointer;
          "
        >
          Run Console Test
        </button>
      </div>
    </div>

    <script src="js/app.js"></script>
    <script>
      // Test function for this page only
      function testStarRating() {
        console.log("=== Testing Star Rating System ===");

        const testCases = [
          { input: 4.5, expected: "4 full + 1 half" },
          { input: 4.2, expected: "4 full + 1 half (normalized from 4.2)" },
          { input: 3.1, expected: "3 full + 1 half (normalized from 3.1)" },
          { input: 35, expected: "5 full (normalized from 35)" },
          { input: 50, expected: "5 full (normalized from 50)" },
          { input: 22, expected: "5 full (normalized from 22)" },
          { input: 0, expected: "0 full + 1 half (normalized from 0)" },
          { input: -5, expected: "0 full + 1 half (normalized from -5)" },
          {
            input: 2.7,
            expected: "2 full + 1 three-quarter (normalized to 3)",
          },
          { input: 1.1, expected: "1 full + 1 half (normalized from 1.1)" },
        ];

        testCases.forEach((testCase) => {
          const normalized = normalizeRating(testCase.input);
          console.log(
            `Input: ${testCase.input} → Normalized: ${normalized} → Expected: ${testCase.expected}`
          );

          // Create a temporary div to test the HTML output
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = generateStarRating(testCase.input);
          const starsContainer = tempDiv.querySelector(".stars-container");
          const fullStars =
            starsContainer.querySelectorAll(".full-star").length;
          const halfStars =
            starsContainer.querySelectorAll(".half-star").length;
          const quarterStars =
            starsContainer.querySelectorAll(".quarter-star").length;
          const threeQuarterStars = starsContainer.querySelectorAll(
            ".three-quarter-star"
          ).length;
          const emptyStars =
            starsContainer.querySelectorAll(".empty-star").length;

          console.log(
            `  → Result: ${fullStars} full, ${halfStars} half, ${quarterStars} quarter, ${threeQuarterStars} three-quarter, ${emptyStars} empty`
          );
          console.log(
            `  → Total stars: ${
              fullStars +
              halfStars +
              quarterStars +
              threeQuarterStars +
              emptyStars
            }`
          );
          console.log("---");
        });

        console.log("=== Test Complete ===");
      }

      // Run tests when page loads
      document.addEventListener("DOMContentLoaded", function () {
        // Test cases
        const testCases = [
          { input: 4.5, elementId: "test-4-5" },
          { input: 4.2, elementId: "test-4-2" },
          { input: 3.1, elementId: "test-3-1" },
          { input: 35, elementId: "test-35" },
          { input: 50, elementId: "test-50" },
          { input: 22, elementId: "test-22" },
          { input: 0, elementId: "test-0" },
          { input: -5, elementId: "test-negative" },
          { input: 2.7, elementId: "test-2-7" },
          { input: 1.1, elementId: "test-1-1" },
        ];

        testCases.forEach((testCase) => {
          const element = document.getElementById(testCase.elementId);
          if (element) {
            const originalRating = parseFloat(testCase.input) || 0;
            const normalizedRating = normalizeRating(testCase.input);
            element.innerHTML = `
                        <div style="margin-bottom: 10px;">
                            <strong>Original:</strong> ${originalRating} → <strong>Normalized:</strong> ${normalizedRating}
                        </div>
                        ${generateStarRating(testCase.input)}
                    `;
          }
        });
      });
    </script>
  </body>
</html>
