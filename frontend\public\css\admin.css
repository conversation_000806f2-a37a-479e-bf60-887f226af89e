:root {
  --bg-color: #f4f7f9;
  --container-bg: #fff;
  --text-color: #333;
  --heading-color: #2c3e50;
  --subheading-color: #34495e;
  --paragraph-color: #7f8c8d;
  --label-color: #555;
  --border-color: #e0e0e0;
  --input-border: #ccc;
  --input-focus-border: #3498db;
  --primary-button-bg: #3498db;
  --primary-button-hover-bg: #2980b9;
  --remove-button-bg: #e74c3c;
  --remove-button-hover-bg: #c0392b;
  --checkbox-border: #3498db;
  --checkbox-checked-bg: #3498db;
  --category-item-bg: #f0f3f5;
  --category-item-border: #e9ecef;
  --category-item-hover-bg: #e2e6ea;
  --selected-category-row-bg: #f0f0f0;
  --selected-category-row-border: #dcdcdc;
}

body.dark-theme {
  --bg-color: #0f0f23; /* primary-bg from index.css */
  --container-bg: #1a1a2e; /* secondary-bg from index.css */
  --text-color: #ffffff; /* text-color from index.css */
  --heading-color: #ffffff; /* text-color from index.css */
  --subheading-color: #ffffff; /* text-color from index.css */
  --paragraph-color: #bdc3c7; /* slightly lighter than text-color for paragraphs */
  --label-color: #ffffff; /* text-color from index.css */
  --border-color: rgba(0, 212, 255, 0.2); /* Accent color with transparency */
  --input-border: #1e1e42; /* card-bg from index.css */
  --input-focus-border: #00d4ff; /* accent-color from index.css */
  --primary-button-bg: #00d4ff; /* accent-color from index.css */
  --primary-button-hover-bg: #00a8cc; /* darker accent for hover */
  --remove-button-bg: #e74c3c; /* Keep red for danger */
  --remove-button-hover-bg: #c0392b; /* Keep red for danger */
  --checkbox-border: #00d4ff; /* accent-color from index.css */
  --checkbox-checked-bg: #00d4ff; /* accent-color from index.css */
  --category-item-bg: #1e1e42; /* card-bg from index.css */
  --category-item-border: rgba(0, 212, 255, 0.1); /* Lighter accent border */
  --category-item-hover-bg: #2a2a4a; /* Slightly lighter card-bg for hover */
  --selected-category-row-bg: #1e1e42; /* card-bg from index.css */
  --selected-category-row-border: rgba(
    0,
    212,
    255,
    0.2
  ); /* Accent color with transparency */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  margin: 0;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  transition: background-color 0.3s, color 0.3s;
}

.container {
  width: 100%;
  max-width: 800px;
  background-color: var(--container-bg);
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s;
}

h1 {
  text-align: center;
  color: var(--heading-color);
  margin-bottom: 30px;
  transition: color 0.3s;
}

.section {
  margin-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
  transition: border-color 0.3s;
}

.section:last-child {
  border-bottom: none;
}

h2 {
  color: var(--subheading-color);
  font-size: 1.5em;
  margin-bottom: 10px;
  transition: color 0.3s;
}

p {
  color: var(--paragraph-color);
  font-size: 0.9em;
  margin-bottom: 20px;
  transition: color 0.3s;
}

label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--label-color);
  transition: color 0.3s;
}

input[type="text"],
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: 6px;
  box-sizing: border-box;
  font-size: 1em;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  background-color: var(
    --container-bg
  ); /* Ensure input background matches container */
  color: var(--text-color);
}

input[type="text"]:focus,
select:focus,
textarea:focus {
  border-color: var(--input-focus-border);
  outline: none;
}

textarea {
  resize: vertical;
}

.options label {
  display: block;
  margin-bottom: 12px;
  font-weight: normal;
}

input[type="radio"] {
  margin-right: 10px;
}

.category-selection-container {
  margin-bottom: 20px;
}

.category-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  margin-top: 15px;
  max-height: 200px; /* Limit height to make it scrollable if many categories */
  overflow-y: auto; /* Enable vertical scrolling */
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 10px;
  background-color: var(--category-item-bg); /* Use variable for consistency */
  transition: background-color 0.3s, border-color 0.3s;
}

.category-checkbox-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  background-color: var(--category-item-bg);
  border-radius: 5px;
  border: 1px solid var(--category-item-border);
  transition: background-color 0.2s ease-in-out, border-color 0.3s;
}

.category-checkbox-item:hover {
  background-color: var(--category-item-hover-bg);
}

.category-checkbox-item input[type="checkbox"] {
  margin-right: 8px;
  /* Custom checkbox styling */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--checkbox-border);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent; /* Ensure checkbox background is transparent by default */
  transition: background-color 0.3s, border-color 0.3s;
}

.category-checkbox-item input[type="checkbox"]:checked {
  background-color: var(--checkbox-checked-bg);
  border-color: var(--checkbox-checked-bg);
}

.category-checkbox-item input[type="checkbox"]:checked::after {
  content: "\2713"; /* Checkmark character */
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.category-checkbox-item label {
  margin-bottom: 0; /* Override default label margin */
  cursor: pointer;
  font-weight: normal;
  color: var(--text-color); /* Use text color variable */
  flex-grow: 1; /* Allow label to take available space */
  transition: color 0.3s;
}

.category-config-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px 15px;
  border: 1px solid var(--selected-category-row-border);
  border-radius: 8px;
  background-color: var(--selected-category-row-bg);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s, border-color 0.3s;
}

.category-config-row span {
  font-weight: 600;
  color: var(--text-color); /* Use text color variable */
  flex-grow: 1; /* Allow category name to take space */
  margin-right: 10px; /* Space between name and select */
  transition: color 0.3s;
}

.category-config-row select.category-strategy-select {
  width: auto; /* Adjust width based on content */
  flex-shrink: 0; /* Prevent shrinking */
  margin-right: 10px; /* Space between select and remove button */
  padding: 8px 12px; /* Adjust padding for better look */
  border: 1px solid var(--input-border);
  border-radius: 6px;
  background-color: var(--container-bg); /* Match container background */
  color: var(--text-color);
  font-size: 0.9em;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
}

#selected-categories-container {
  margin-top: 20px;
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
  transition: border-color 0.3s;
}

.status-message {
  padding: 10px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  display: none; /* Hidden by default */
}

.status-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.remove-category-btn {
  background-color: var(--remove-button-bg);
  color: white;
  border: none;
  padding: 8px 12px; /* Adjusted padding */
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9em; /* Smaller font for button */
}

.remove-category-btn:hover {
  background-color: var(--remove-button-hover-bg);
}

button[type="submit"] {
  width: 100%;
  padding: 15px;
  background-color: var(--primary-button-bg);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

button[type="submit"]:hover {
  background-color: var(--primary-button-hover-bg);
}

/* Tablet optimizations for admin panel */
@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    max-width: 90%;
    padding: 30px;
  }

  .category-checkboxes {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    max-height: 250px;
  }

  .category-config-row {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .category-strategy-select {
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .remove-category-btn {
    align-self: flex-end;
    width: auto;
  }
}

/* Large tablet/small desktop */
@media (min-width: 1025px) and (max-width: 1366px) {
  .container {
    max-width: 85%;
  }

  .category-checkboxes {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}
