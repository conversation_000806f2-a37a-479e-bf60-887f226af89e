<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Suggested Prompts</title>
    <style>
      /* Basic styling for testing */
      body {
        font-family: Arial, sans-serif;
        background: #0f0f23;
        color: white;
        padding: 20px;
      }

      .test-container {
        max-width: 600px;
        margin: 0 auto;
        background: #1a1a2e;
        padding: 20px;
        border-radius: 10px;
      }

      #messages {
        min-height: 200px;
        background: #16213e;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
      }

      .message {
        padding: 8px 12px;
        border-radius: 15px;
        margin-bottom: 10px;
        max-width: 80%;
      }

      .user-message {
        background-color: #00d4ff;
        color: white;
        margin-left: auto;
      }

      .agent-message {
        background-color: #2a2a4a;
        color: white;
      }

      #messageForm {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      #message {
        flex: 1;
        padding: 10px;
        border: 1px solid #333;
        border-radius: 8px;
        background: #2a2a4a;
        color: white;
        resize: vertical;
        min-height: 40px;
      }

      button {
        padding: 10px 15px;
        background: #00d4ff;
        color: black;
        border: none;
        border-radius: 8px;
        cursor: pointer;
      }

      /* Copy suggested prompts styles from main CSS */
      .suggested-prompts-container {
        margin-top: 15px;
        opacity: 1;
        transition: opacity 0.3s ease, max-height 0.3s ease;
        max-height: 200px;
        overflow: hidden;
      }

      .suggested-prompts-container.hidden {
        opacity: 0;
        max-height: 0;
        margin-top: 0;
      }

      .suggested-prompts-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: flex-start;
        align-items: flex-start;
      }

      .suggested-prompt-button {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.12);
        border-radius: 20px;
        padding: 8px 16px;
        color: white;
        font-size: 0.9rem;
        font-family: inherit;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        max-width: 280px;
        overflow: hidden;
        text-overflow: ellipsis;
        backdrop-filter: blur(10px);
      }

      .suggested-prompt-button:hover {
        background: rgba(0, 212, 255, 0.15);
        border-color: rgba(0, 212, 255, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
      }

      .suggested-prompt-button:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(0, 212, 255, 0.15);
      }

      .suggested-prompt-button {
        animation: fadeInUp 0.4s ease forwards;
        opacity: 0;
        transform: translateY(10px);
      }

      .suggested-prompt-button:nth-child(1) {
        animation-delay: 0.1s;
      }
      .suggested-prompt-button:nth-child(2) {
        animation-delay: 0.15s;
      }
      .suggested-prompt-button:nth-child(3) {
        animation-delay: 0.2s;
      }
      .suggested-prompt-button:nth-child(4) {
        animation-delay: 0.25s;
      }

      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .test-controls {
        margin-top: 20px;
        padding: 15px;
        background: #16213e;
        border-radius: 8px;
      }

      .test-controls button {
        margin: 5px;
        background: #333;
        color: white;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Suggested Prompts Test</h1>

      <div id="messages"></div>

      <form id="messageForm">
        <textarea
          id="message"
          placeholder="Ask me anything about products..."
          rows="1"
        ></textarea>
        <button type="submit">Send</button>
      </form>

      <!-- Suggested Prompts Container -->
      <div class="suggested-prompts-container" id="suggestedPromptsContainer">
        <div class="suggested-prompts-grid" id="suggestedPromptsGrid">
          <!-- Prompts will be dynamically inserted here -->
        </div>
      </div>

      <div class="test-controls">
        <h3>Test Controls:</h3>
        <button onclick="showSuggestedPrompts()">Show Prompts</button>
        <button onclick="hideSuggestedPrompts()">Hide Prompts</button>
        <button onclick="clearMessages()">Clear Messages</button>
        <button onclick="addTestMessage()">Add Test Message</button>
      </div>
    </div>

    <script>
      // Copy the suggested prompts data and functions from main app.js
      const SUGGESTED_PROMPTS = {
        discovery: [
          {
            display: "Popular whiskey brands",
            fullPrompt:
              "What are your most popular whiskey brands? I'd like to know which ones are bestsellers and what makes them special.",
          },
          {
            display: "Wines under $30",
            fullPrompt:
              "Can you show me some good wines under $30? I'm looking for quality options that won't break the bank.",
          },
        ],
        recommendations: [
          {
            display: "Wine for dinner",
            fullPrompt:
              "Can you recommend a wine for dinner tonight? I'm having [describe your meal] and want something that pairs well.",
          },
          {
            display: "Party cocktails",
            fullPrompt:
              "Can you suggest some cocktails for a party? I need drinks that are crowd-pleasers and not too complicated to make.",
          },
        ],
        education: [
          {
            display: "Types of whiskey",
            fullPrompt:
              "Tell me about different types of whiskey. I want to understand the differences between Scotch, bourbon, rye, and other varieties.",
          },
          {
            display: "Perfect Old Fashioned",
            fullPrompt:
              "How do I make a perfect Old Fashioned cocktail? I want to learn the proper technique and ingredient ratios.",
          },
        ],
        pairing: [
          {
            display: "Spirits with chocolate",
            fullPrompt:
              "What spirits pair well with chocolate? I'm planning a dessert course and want to know which drinks complement chocolate best.",
          },
          {
            display: "Wine for steak",
            fullPrompt:
              "What wine do you recommend with steak? I'm cooking a nice cut and want the perfect wine pairing to enhance the meal.",
          },
        ],
      };

      const messageInput = document.getElementById("message");
      const messagesDiv = document.getElementById("messages");
      const suggestedPromptsContainer = document.getElementById(
        "suggestedPromptsContainer"
      );
      const suggestedPromptsGrid = document.getElementById(
        "suggestedPromptsGrid"
      );

      function autoResize() {
        messageInput.style.height = "auto";
        messageInput.style.height =
          Math.min(messageInput.scrollHeight, 120) + "px";
      }

      function showSuggestedPrompts() {
        if (!suggestedPromptsContainer || !suggestedPromptsGrid) return;

        if (messagesDiv.children.length > 0) {
          hideSuggestedPrompts();
          return;
        }

        suggestedPromptsGrid.innerHTML = "";
        const selectedPrompts = getRandomPrompts();

        selectedPrompts.forEach((promptObj) => {
          const button = document.createElement("button");
          button.className = "suggested-prompt-button";
          button.textContent = promptObj.display; // Show the short display text
          button.setAttribute("data-display", promptObj.display);
          button.setAttribute("data-full-prompt", promptObj.fullPrompt);
          button.addEventListener("click", () => handlePromptClick(promptObj));
          suggestedPromptsGrid.appendChild(button);
        });

        suggestedPromptsContainer.classList.remove("hidden");
      }

      function hideSuggestedPrompts() {
        if (!suggestedPromptsContainer) return;
        suggestedPromptsContainer.classList.add("hidden");
      }

      function getRandomPrompts() {
        const allPrompts = [];
        const categories = Object.keys(SUGGESTED_PROMPTS);

        categories.forEach((category) => {
          const categoryPrompts = SUGGESTED_PROMPTS[category];
          const shuffled = [...categoryPrompts].sort(() => 0.5 - Math.random());
          allPrompts.push(...shuffled.slice(0, 2));
        });

        return allPrompts.sort(() => 0.5 - Math.random()).slice(0, 8);
      }

      function handlePromptClick(promptObj) {
        // Use the full prompt text for the actual message (like Gemini does)
        const fullPrompt = promptObj.fullPrompt;

        // Hide the suggested prompts immediately
        hideSuggestedPrompts();

        // Add the full prompt as a user message
        addMessage(fullPrompt, "user");

        // Simulate AI response for testing
        setTimeout(() => {
          addMessage(
            "This is a simulated AI response to: " + promptObj.display,
            "agent"
          );
        }, 1000);
      }

      function addMessage(text, sender) {
        const messageElement = document.createElement("div");
        messageElement.classList.add("message", `${sender}-message`);
        messageElement.textContent = text;
        messagesDiv.appendChild(messageElement);
        hideSuggestedPrompts();
      }

      // Test functions
      function clearMessages() {
        messagesDiv.innerHTML = "";
        showSuggestedPrompts();
      }

      function addTestMessage() {
        addMessage("This is a test message", "user");
      }

      // Initialize
      messageInput.addEventListener("input", () => {
        autoResize();
        if (messageInput.value.trim().length > 0) {
          hideSuggestedPrompts();
        } else if (messagesDiv.children.length === 0) {
          showSuggestedPrompts();
        }
      });

      document.getElementById("messageForm").addEventListener("submit", (e) => {
        e.preventDefault();
        const message = messageInput.value.trim();
        if (message) {
          addMessage(message, "user");
          messageInput.value = "";
          autoResize();
        }
      });

      // Show prompts initially
      showSuggestedPrompts();
    </script>
  </body>
</html>
