#backend/app/main
__import__('pysqlite3')
import sys
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

import os
import json
import base64
import warnings
import logging
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv
from typing import Optional, Tuple, Dict, AsyncGenerator
import asyncio
import chromadb

from google.genai import types
from google.genai.types import (
    Part,
    Content,
    Blob,
)
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService 
from google.adk.agents.live_request_queue import LiveRequestQueue
from google.adk.agents.run_config import RunConfig
from google.adk.sessions import Session


from fastapi import FastAPI, Request, Query
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware

# Import your root agent from its location
from jmsc_concierge.agent import root_agent


CHROMA_HOST = os.getenv("CHROMA_HOST", "chroma")
CHROMA_PORT = os.getenv("CHROMA_PORT", 8000)
CHROMA_COLLECTION = os.getenv("CHROMA_COLLECTION", "products")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")

# ======================================================================
# GLOBAL SETUP: APP NAME AND LOGGING
# ======================================================================

warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")
load_dotenv()
APP_NAME = "JMSC's Concierge"

LOG_DIR = Path("logs")
LOG_DIR.mkdir(parents=True, exist_ok=True)
LOG_FILE = LOG_DIR / "session.log"
SESSION_INFO_FILE = LOG_DIR / "session_info.log"

root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)

# Clear existing handlers to prevent duplicate logs or conflicts with default handlers (e.g., from uvicorn)
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# Use TimedRotatingFileHandler for daily log rotation
# 'midnight' means rotate at midnight, 1 means rotate every day
file_handler = TimedRotatingFileHandler(LOG_FILE, when='midnight', interval=1, encoding='utf-8')
console_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

logger = logging.getLogger(__name__)


# ======================================================================
# ADK CORE LOGIC
# ======================================================================
# Keep runner instantiated globally for InMemorySessionService
session_service = InMemorySessionService()

initial_state = {
    "products_data": {
        "status": "success",
        "products": []
    },
    "recipe_data": {
        "status": "success",
        "data": {"recipe": []}
    }
}
runner = Runner(
    app_name=APP_NAME,
    agent=root_agent,
    session_service=session_service
)


async def start_agent_session_and_run_live(user_id: str, is_audio: bool = False, session_id: Optional[str] = None) -> Tuple[LiveRequestQueue, Session]:
    """Initializes/resumes ADK session and returns live_request_queue and Session."""
    session = None
    if session_id:
        try:
            session = await runner.session_service.get_session(app_name=APP_NAME, user_id=user_id, session_id=session_id)
            logger.info(f"Resuming ADK session: {session.id} for user: {user_id}")
        except Exception:
            logger.warning(f"Existing session {session_id} not found for user {user_id}. Creating new session.")
    
    if not session:
        session = await runner.session_service.create_session(
            app_name=APP_NAME,
            user_id=user_id,
            state=initial_state
        )
        logger.info(f"Created new ADK session: {session.id} for user: {user_id}")

    modality = "AUDIO" if is_audio else "TEXT"

    # create a speech config with voice settings
    speech_config = types.SpeechConfig(
        voice_config=types.VoiceConfig(
            # Puck, Charon, Kore, Fenrir, Aoede, Leda, Orus and Zephyr
            prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Puck")
        )
    )
    # create a run config with basic settings
    config = {"response_modalities": [modality], "speech_config": speech_config}

    # Add output_audio_transcription when audio is enabled to get both audio and text
    if is_audio:
        config["output_audio_transcription"] = {}
        config["input_audio_transcription"] = {}

    run_config = RunConfig(**config)
    live_request_queue = LiveRequestQueue()

    # This starts the ADK's live run and will yield events
    live_events = runner.run_live(
        session=session,
        live_request_queue=live_request_queue,
        run_config=run_config,
    )
    return live_events, live_request_queue, session


async def agent_to_client_sse_stream(live_events, session: Session) -> AsyncGenerator[str, None]:
    """Consumes events from the ADK and yields them as SSE messages."""
    try:
        async for event in live_events:
            # --- Reactive Logic for Final Product Data (from state_delta) ---
            if event.actions and event.actions.state_delta:
                state_changes = event.actions.state_delta
                if "products_data" in state_changes:
                    enriched_data = state_changes["products_data"]
                    if isinstance(enriched_data, dict) and enriched_data.get("status") == "success" and enriched_data.get("products"):
                        json_message_for_ui = {
                            "mime_type": "application/json",
                            "data": enriched_data
                        }
                        yield f"data: {json.dumps(json_message_for_ui)}\n\n"
                        # logger.info(f"[AGENT TO CLIENT]: JSON MESSAGE FOR UI: {json_message_for_ui}")
                    elif isinstance(enriched_data, list) and enriched_data:
                        # if it's a list, wrap it in the expected format
                        formatted_data = {
                            "status": "success",
                            "products": enriched_data
                        }
                        json_message_for_ui = {
                            "mime_type": "application/json",
                            "data": formatted_data
                        }
                        yield f"data: {json.dumps(json_message_for_ui)}\n\n"
                        # logger.info(f"[AGENT TO CLIENT]: JSON MESSAGE FOR UI: {json_message_for_ui}")
                elif "recipe_data" in state_changes:
                    logger.info("Detected 'recipe_data' state delta. Processing for UI push...")
                    recipe_enriched_data = state_changes["recipe_data"]
                    # logger.info(recipe_enriched_data)
                    clean_recipe_enriched_data = recipe_enriched_data.strip().removeprefix("```json").removesuffix("```").strip()
                    json_message_for_ui = {
                        "mime_type": "application/json",
                        "data": json.loads(clean_recipe_enriched_data)
                    }
                    yield f"data: {json.dumps(json_message_for_ui)}\n\n"
                    # logger.info("[AGENT TO CLIENT]: JSON MESSAGE FOR UI: %s", json_message_for_ui)

            # --- Handle Streaming Content (Audio/Text) ---
            # NOTE: Be careful with event.content.parts[0] if there might be multiple parts or no parts.
            part: Optional[Part] = None
            if event.content and event.content.parts:
                part = event.content.parts[0]

            if part:
                # Handle streaming audio
                if part.inline_data and part.inline_data.mime_type.startswith("audio/pcm"):
                    audio_data = part.inline_data.data
                    if audio_data:
                        message = {
                            "mime_type": "audio/pcm",
                            "data": base64.b64encode(audio_data).decode("ascii"),
                        }
                        yield f"data: {json.dumps(message)}\n\n"

                # # Handle streaming text
                if part.text and part.text.strip():  # event.partial is important for text streaming
                    role = "agent"
                    if event.content and event.content.role == "user":
                        role = "user"

                    # Check if there's any text to process
                    if role == "user" or (role == "agent" and event.partial):
                        # if role == "user":
                        #     logger.info(f"[CLIENT TRANSCRIPT]: {part.text}")
                        # else:
                        #     logger.info(f"[AGENT TRANSCRIPT]: {part.text}")

                        message = {
                            "mime_type": "text/plain",
                            "data": part.text,
                            "role": role
                        }
                        yield f"data: {json.dumps(message)}\n\n"

            # --- Handle Turn Completion Signal ---
            # This should generally come AFTER content if it exists, as it signals end of a message chunk.
            if event.turn_complete or event.interrupted:
                # This 'turn_complete' message tells the client no more parts for this specific turn.
                yield f"data: {json.dumps({'turn_complete': event.turn_complete, 'interrupted': event.interrupted})}\n\n"

    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in agent_to_client_sse_stream for session {session.id}: {error_message}", exc_info=True)

        # Check for specific 1011 WebSocket timeout errors
        if ("1011" in error_message and "Deadline expired" in error_message) or \
           ("internal error" in error_message and "Deadline expired" in error_message):
            logger.warning(f"Detected ADK timeout error (1011 - Deadline expired) for session {session.id}")
            # Send specific timeout error to client
            yield f"data: {json.dumps({'event_type': 'error', 'error_type': 'timeout', 'message': error_message})}\n\n"
        else:
            # Send generic error to client
            yield f"data: {json.dumps({'event_type': 'error', 'error_type': 'generic', 'message': f'Stream error: {error_message}'})}\n\n"


# ======================================================================
# FASTAPI WEB SERVER
# ======================================================================

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# LOG CLEANUP
async def cleanup_old_logs(log_dir: Path, retention_days: int = 5):
    """
    Cleans up log files older than retention_days in the specified directory.
    """
    logger.info(f"Starting log cleanup for directory: {log_dir}")
    cutoff_date = datetime.now() - timedelta(days=retention_days)

    for log_file in log_dir.iterdir():
        if log_file.is_file() and log_file.name.startswith("session.log."):
            try:
                # Extract date from filename (e.g., session.log.2025-07-17)
                date_str = log_file.name.replace("session.log.", "")
                file_date = datetime.strptime(date_str, "%Y-%m-%d")

                if file_date < cutoff_date:
                    os.remove(log_file)
                    logger.info(f"Deleted old log file: {log_file.name}")
            except ValueError:
                # Not a date-suffixed log file, or format is unexpected
                logger.warning(f"Skipping non-standard log file during cleanup: {log_file.name}")
            except OSError as e:
                logger.error(f"Error deleting log file {log_file.name}: {e}")
    logger.info("Log cleanup finished")


@app.on_event("startup")
async def startup_event():
    # Schedule log cleanup to run periodically (e.g., every 24 hours)
    # This runs in the background and does not block the main application
    asyncio.create_task(periodic_log_cleanup())


async def periodic_log_cleanup():
    while True:
        await cleanup_old_logs(LOG_DIR, retention_days=5)
        await asyncio.sleep(24 * 60 * 60) # Run every 24 hours (in seconds)


active_sessions: Dict[str, Tuple[LiveRequestQueue, str]] = {}

CONFIG_FILE_PATH = Path(__file__).parent / "config.json"

# Default configuration
DEFAULT_CONFIG = {
    "selected_categories": [],
    "recommendation_preference": "all_products",
    "chat_persona": "friendly_bartender",
    "welcome_message": "",
    "store_name": "",
    "theme": "light"
}

# Hardcoded categories for demonstration. In a real app, these would come from a DB.
ALL_CATEGORIES = [
    "Vodka", "Beer", "Whiskey", "Wine", "Gin", "Rum", "Tequila",
    "Brandy", "Liqueurs", "Sake", "Champagne", "Cognac", "Mezcal",
    "Cider", "Non-Alcoholic", "Accessories"
]

@app.get("/product_details/{upc}")
async def get_product_details(upc: str):
    """Retrieves full product details for a specific UPC directly from the database."""
    logger.info(f"Received request for product details for UPC: {upc}")
    try:
        client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
        collection = client.get_collection(name=CHROMA_COLLECTION)

        # Fetch the item by its ID (which is the UPC)
        product_data = collection.get(
            ids=[upc],
            include=["metadatas", "documents"]
        )

        if not product_data or not product_data.get("ids"):
            logger.warning(f"Product with UPC {upc} not found in ChromaDB")
            return JSONResponse(status_code=404, content={"status": "error", "message": "Product not found"})
        
        # The data is nested, so we need to extract it.
        metadata = product_data["metadatas"][0]

        # The metadata itself contains JSON strings that need to parsed.
        def safe_json_parse(json_str):
            if isinstance(json_str, str):
                try:
                    return json.loads(json_str)
                except (json.JSONDecodeError, ValueError):
                    return json_str
            return json_str
        
        images_data = safe_json_parse(metadata.get("images", "[]"))
        product_insight = safe_json_parse(metadata.get("productInsight", "{}"))
        pack_size_detail = safe_json_parse(metadata.get("packSizeDetail", "{}"))
        size_data = safe_json_parse(metadata.get("size", "{}"))

        # Construct a clean product object
        product_info = {
            "id": product_data["ids"][0],
            "item_name": metadata.get("db_item_name"),
            "name": metadata.get("name"),
            "brandName": metadata.get("brandName"),
            "sale_price": metadata.get("db_sale_price"),
            "size": size_data.get("sizeValue") if isinstance(size_data, dict) else None,
            "main_category": metadata.get("db_main_category"),
            "description": metadata.get("description"),
            "images": images_data,
            "productInsight": product_insight,
            "pairingInfo": metadata.get("pairingInfo"),
            "packSizeDetail": pack_size_detail
        }
        logger.info(f"Successfully retrieved product {upc}.")
        return JSONResponse(content={"status": "success", "data": product_info})
    except Exception as e:
        logger.error(f"Error retrieving product details for UPC {upc}: {e}", exc_info=True)
        return JSONResponse(status_code=500, content={"status": "error", "message": "An unexpected server error occurred."})


@app.get("/admin_api/categories")
async def get_categories():
    """Returns a list of all available product categories."""
    return JSONResponse(content={"categories": ALL_CATEGORIES})


@app.get("/admin_api/config")
async def get_config():
    """Retrieves the current system configuration."""
    config_data = DEFAULT_CONFIG.copy()
    if CONFIG_FILE_PATH.exists():
        with open(CONFIG_FILE_PATH, "r", encoding="utf-8") as f:
            try:
                file_config = json.load(f)
                config_data.update(file_config) # Merge file config with defaults
            except json.JSONDecodeError:
                logger.error("Error decoding config.json. Using default configuration.")
    return JSONResponse(content=config_data)


@app.post("/admin_api/config")
async def set_config(request: Request):
    """Saves the new system configuration."""
    try:
        new_config = await request.json()
        
        # Load existing config or use default
        current_config = DEFAULT_CONFIG.copy()
        if CONFIG_FILE_PATH.exists():
            with open(CONFIG_FILE_PATH, "r", encoding="utf-8") as f:
                try:
                    current_config.update(json.load(f))
                except json.JSONDecodeError:
                    pass # Will use default if file is corrupt

        # Update only the fields that are present in the new_config
        for key, value in new_config.items():
            if key in current_config: # Only update known config keys
                current_config[key] = value

        with open(CONFIG_FILE_PATH, "w", encoding="utf-8") as f:
            json.dump(current_config, f, indent=2)
        
        logger.info(f"Configuration saved: {current_config}")
        return JSONResponse(content={"success": True, "message": "Configuration saved.", "config": current_config})
    except Exception as e:
        logger.error(f"Error saving configuration: {e}", exc_info=True)
        return JSONResponse(content={"success": False, "error": str(e)}, status_code=500)


# --- THIS IS THE CORRECTED SSE ENDPOINT ---
@app.get("/events/{user_id}")
async def sse_endpoint(user_id: str, is_audio: bool = Query(False), session_id: Optional[str] = Query(None)):
    """Handles the Server-Sent Events connection from the client."""
    logger.info(f"Received GET /events for user: {user_id}, Audio mode: {is_audio}, Session ID (from client): {session_id}")
    
    async def event_generator_for_sse():
        live_events_gen = None # Initialize outside try for finally block
        live_request_queue = None
        session = None

        try:
            # Start/resume ADK session and get the live_events generator
            live_events_gen, live_request_queue, session = await start_agent_session_and_run_live(user_id, is_audio, session_id)
            # Store queue and actual session ID
            active_sessions[user_id] = (live_request_queue, session.id)
            
            # --- Yield initial session_init message first ---
            yield f"data: {json.dumps({'event_type': 'session_init', 'sessionId': session.id, 'userId': user_id})}\n\n"
            
            logger.info(f"Client #{user_id} connected via SSE. Actual Session ID: {session.id}")

            # --- Now, yield events from the agent_to_client_sse_stream ---
            # This is correct; it consumes the generator and re-yields its items.
            async for data in agent_to_client_sse_stream(live_events_gen, session):
                yield data
                
        except Exception as e:
            error_message = str(e)
            logger.error(f"Error in SSE endpoint for user {user_id}: {error_message}", exc_info=True)

            # Check for specific 1011 WebSocket timeout errors
            if ("1011" in error_message and "Deadline expired" in error_message) or \
               ("internal error" in error_message and "Deadline expired" in error_message):
                logger.warning(f"Detected ADK timeout error (1011 - Deadline expired) for user {user_id}")
                # Send specific timeout error to client
                yield f"data: {json.dumps({'event_type': 'error', 'error_type': 'timeout', 'message': error_message})}\n\n"
            else:
                # Send generic error to client
                yield f"data: {json.dumps({'event_type': 'error', 'error_type': 'generic', 'message': f'Server error: {error_message}'})}\n\n"
        finally:
            # This finally block runs after the generator is exhausted or an error occurs.
            # Ensure cleanup happens even if an exception occurs during the stream.
            if user_id in active_sessions and (session and active_sessions[user_id][1] == session.id): 
                # Check session is not None for safety in finally
                active_sessions[user_id][0].close() # Close LiveRequestQueue
                del active_sessions[user_id]
            logger.info(f"Client #{user_id} disconnected via SSE (Session: {session.id if session else 'N/A'}).")

    # --- The endpoint function now directly returns the StreamingResponse ---
    # This is the key change to fix the SyntaxError.
    return StreamingResponse(event_generator_for_sse(), media_type="text/event-stream")


@app.get("/product_data/{user_id}/{session_id}/{upc}")
async def get_product_data(user_id: str, session_id: str, upc: str):
    """Retrieves product data for a specific UPC from the session state."""
    logger.info(f"Received request for product data for user: {user_id}, Session ID: {session_id}, UPC: {upc}")
    try:
        session = await runner.session_service.get_session(
            app_name=APP_NAME,
            user_id=user_id,
            session_id=session_id
        )
    except Exception as e:
        logger.warning(f"Session not found for user {user_id}, session {session_id}: {e}")
        return {"error": "Session not found or has expired.", "status": "error"}

    product_data_in_state = session.state.get("products_data")
    if not product_data_in_state:
        logger.warning(f"No 'products_data' found in session state for user {user_id}")
        return {"error": "No product data available in session.", "status": "error"}

    try:
        parsed_session_data = None
        if isinstance(product_data_in_state, str):
            clean_str = product_data_in_state.strip().removeprefix("```json").removesuffix("```").strip()
            parsed_session_data = json.loads(clean_str)
        elif isinstance(product_data_in_state, dict):
            parsed_session_data = product_data_in_state
        else:
            raise TypeError("products_data in session state is neither string nor dict.")

        products_list = parsed_session_data.get("data", {}).get("products", [])

        if not isinstance(products_list, list):
            raise TypeError("Products list within 'products_data' is not a list.")

        for product in products_list:
            # Check both 'upc' and 'item_code' fields since products might use either
            if product.get("upc") == upc or product.get("item_code") == upc:
                logger.info(f"Found product with UPC/item_code {upc} in session data for user {user_id}.")
                return {"status": "success", "data": product}

        logger.warning(f"Product with UPC/item_code {upc} not found in session data for user {user_id}")
        return {"error": f"Product with UPC/item_code {upc} not found in session data.", "status": "error"}

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse 'products_data' from session state for user {user_id}: {e}", exc_info=True)
        return {"error": "Failed to parse product data from session.", "status": "error"}
    except Exception as e:
        logger.error(f"Error retrieving product data for user {user_id}, UPC {upc}: {e}", exc_info=True)
        return {"error": "An unexpected server error occurred.", "status": "error"}


@app.post("/send/{user_id}/{session_id}")
async def send_message_endpoint(user_id: str, session_id: str, request: Request):
    # logger.info(f"Received POST request to /send/{user_id}/{session_id}")
    session_data = active_sessions.get(user_id)
    if not session_data or session_data[1] != session_id:
        logger.warning(f"No active session found or ID mismatch for user {user_id}, session {session_id}.")
        return {"error": "Session not found or has expired.", "status": "error"}
    
    live_request_queue, stored_session_id = session_data

    try:
        message = await request.json()
        mime_type = message.get("mime_type")
        data = message.get("data")
        # logger.info(f"Received message: mime_type={mime_type}, data={data[:50]}...")

        if mime_type == "text/plain":
            content = Content(role="user", parts=[Part.from_text(text=data)])
            live_request_queue.send_content(content=content)
            logger.info(f"[CLIENT TO AGENT][{user_id}/{session_id}]: {data}")
        elif mime_type == "audio/pcm":
            decoded_data = base64.b64decode(data)
            live_request_queue.send_realtime(Blob(data=decoded_data, mime_type=mime_type))
        else:
            return {"error": f"Mime type {mime_type} not supported."}
        
        return {"status": "sent"}
    
    except Exception as e:
        logger.error(f"Error processing message for user {user_id}/{session_id}: {e}", exc_info=True)
        return {"error": "Failed to process message."}