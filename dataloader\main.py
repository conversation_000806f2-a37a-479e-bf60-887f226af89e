import os
import logging
import mysql.connector
import requests
import chromadb
import json
import time
import hashlib
from decimal import Decimal
from dotenv import load_dotenv
from chromadb.utils import embedding_functions

# --- Basic Setup & Config Loading ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
load_dotenv()
MYSQL_HOST, MYSQL_USER, MYSQL_PASS, MYSQL_DB = os.getenv("MYSQL_HOST"), os.getenv("MYSQL_USER"), os.getenv("MYSQL_PASSWORD"), os.getenv("MYSQL_DATABASE")
API_URL = os.getenv("PRODUCT_API_URL")
CHROMA_HOST, CHROMA_PORT, CHROMA_COLLECTION = os.getenv("CHROMA_HOST"), os.getenv("CHROMA_PORT"), os.getenv("CHROMA_COLLECTION")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
REFRESH_INTERVAL_HOURS = float(os.getenv("REFRESH_INTERVAL_HOURS", 5))


# --- Helper Functions ---
def convert_decimals(obj):
    if isinstance(obj, list): return [convert_decimals(i) for i in obj]
    if isinstance(obj, dict): return {k: convert_decimals(v) for k, v in obj.items()}
    if isinstance(obj, Decimal): return str(obj)
    return obj


def flatten_metadata(metadata):
    flattened_meta = metadata.copy()
    for key, value in flattened_meta.items():
        if isinstance(value, (list, dict)):
            flattened_meta[key] = json.dumps(value)
    return flattened_meta


def fetch_all_products_from_mysql():
    MYSQL_TABLE = os.getenv("MYSQL_TABLE", "items")
    products = []
    try:
        conn = mysql.connector.connect(host=MYSQL_HOST, user=MYSQL_USER, password=MYSQL_PASS, database=MYSQL_DB)
        cursor = conn.cursor(dictionary=True)
        query = f"SELECT item_code, item_name, sale_price, stock_qty, main_category, size FROM {MYSQL_TABLE};"
        cursor.execute(query)
        products = cursor.fetchall()
    except mysql.connector.Error as e:
        logging.error(f"Error fetching from MySQL: {e}")
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()
    return products


def create_rich_document(product_sql, product_api):
    description = product_api.get('description', 'No description available.')
    brand = product_api.get('brandName', '')
    insight = product_api.get('productInsight', '')
    return (f"Product: {brand} {product_sql['item_name']}. "
            f"Category: {product_sql.get('main_category', '')}. "
            f"Description: {description}. "
            f"Expert Insight: {insight}.")


def create_semantic_hash(product_sql, product_api):
    doc_string = create_rich_document(product_sql, product_api)
    return hashlib.sha256(doc_string.encode()).hexdigest()


def fetch_api_details_for_list(item_codes):
    if not item_codes:
        return {}
    logging.info(f"Making targeted API call for {len(item_codes)} products.")
    logging.info(f"API_URL: {API_URL}")
    logging.info(f"Item codes to fetch: {item_codes[:5]}...")  # Show first 5 codes

    headers = {"accept": "*/*", "Content-Type": "application/json"}
    try:
        payload = json.dumps(item_codes)
        logging.info(f"Request payload: {payload[:200]}...")  # Show first 200 chars
        
        response = requests.post(API_URL, headers=headers, data=payload, timeout=30)
        logging.info(f"Response status code: {response.status_code}")
        logging.info(f"Response headers: {dict(response.headers)}")

        if response.status_code != 200:
            logging.error(f"API returned status {response.status_code}: {response.text}")
            return {}

        response.raise_for_status()
        data = response.json()

        logging.info(f"API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        if isinstance(data, dict) and 'products' in data:
            logging.info(f"Number of products in API response: {len(data.get('products', []))}")
            logging.info(f"Sample product: {data.get('products', [])[0]}")
        
        result = {str(d["upc"]): d for d in data.get("products", []) if d.get("upc")}
        logging.info(f"Successfully processed {len(result)} products from API")
        return result
    except requests.exceptions.RequestException as e:
        logging.error(f"Targeted API request failed: {e}")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse API response as JSON: {e}")
        logging.error(f"Raw response: {response.text[:500]}...")
        return {}
    except Exception as e:
        logging.error(f"Unexpected error in API call: {e}")
        return {}


def batch_upsert(collection, ids, documents, metadatas, batch_size=5000):
    """Helper function to upsert data in batches"""
    total_items = len(ids)
    for i in range(0, total_items, batch_size):
        end_idx = min(i + batch_size, total_items)
        batch_ids = ids[i:end_idx]
        batch_docs = documents[i:end_idx]
        batch_metas = metadatas[i:end_idx]

        logging.info(f"Upserting batch {i//batch_size + 1}: items {i+1}-{end_idx} of {total_items}")
        collection.upsert(ids=batch_ids, documents=batch_docs, metadatas=batch_metas)


def batch_update(collection, ids, metadatas, batch_size=5000):
    """Helper function to update metadata in batches"""
    total_items = len(ids)
    for i in range(0, total_items, batch_size):
        end_idx = min(i + batch_size, total_items)
        batch_ids = ids[i:end_idx]
        batch_metas = metadatas[i:end_idx]

        logging.info(f"Updating batch {i//batch_size + 1}: items {i+1}-{end_idx} of {total_items}")
        collection.update(ids=batch_ids, metadatas=batch_metas)


def batch_delete(collection, ids, batch_size=5000):
    """Helper function to delete items in batches"""
    total_items = len(ids)
    for i in range(0, total_items, batch_size):
        end_idx = min(i + batch_size, total_items)
        batch_ids = ids[i:end_idx]

        logging.info(f"Deleting batch {i//batch_size + 1}: items {i+1}-{end_idx} of {total_items}")
        collection.delete(ids=batch_ids)


# --- THE COMBINED, PRODUCTION-GRADE SYNC LOGIC ---
def run_data_sync_cycle():
    logging.info("--- Starting new data sync cycle ---")
    
    # 1. Connect to ChromaDB
    chroma_client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
    sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=EMBEDDING_MODEL)
    collection = chroma_client.get_or_create_collection(name=CHROMA_COLLECTION, embedding_function=sentence_transformer_ef)

    # 2. Get the full state of both sources
    all_sql_products = fetch_all_products_from_mysql()
    if not all_sql_products:
        logging.warning("No products in MySQL. Skipping cycle.")
        return
    
    existing_chroma_data = collection.get(include=["metadatas"])
    
    sql_products_map = {str(p['item_code']): p for p in all_sql_products}
    chroma_products_map = {existing_chroma_data['ids'][i]: meta for i, meta in enumerate(existing_chroma_data['metadatas'])}
    
    current_sql_ids = set(sql_products_map.keys())
    existing_chroma_ids = set(chroma_products_map.keys())

    # 3. Identify exactly which UPCs need API details
    new_item_codes = current_sql_ids - existing_chroma_ids
    # We will use 'api_details_fetched' as our flag.
    # Retry if the flag is missing or false.
    retry_item_codes = {
        item_id for item_id, meta in chroma_products_map.items()
        if not meta.get('api_details_fetched')
    }
    codes_to_fetch = list(new_item_codes | retry_item_codes)
    
    # 4. Fetch API details ONLY for the necessary items
    newly_fetched_api_details = fetch_api_details_for_list(codes_to_fetch)
    
    # 5. Begin comparison loop to prepare batch operations
    ids_to_upsert, docs_to_upsert, metadatas_to_upsert = [], [], []
    ids_to_update, metadatas_to_update = [], []

    for item_code, sql_product in sql_products_map.items():
        if item_code in newly_fetched_api_details:
            api_details = newly_fetched_api_details[item_code]
            api_details['api_details_fetched'] = True  # Set our new flag to True
        else:
            api_details = chroma_products_map.get(item_code, {})

        new_hash = create_semantic_hash(sql_product, api_details)
        
        metadata = api_details.copy()
        metadata['db_item_name'] = sql_product['item_name']
        metadata['db_sale_price'] = sql_product['sale_price']
        metadata['db_stock_qty'] = sql_product['stock_qty']
        metadata['db_main_category'] = sql_product.get('main_category')
        metadata['db_size'] = sql_product.get('size')
        metadata['visibility'] = "visible" if sql_product.get('stock_qty', 0) > 0 else "invisible"
        metadata['semantic_hash'] = new_hash
        # Ensure the fetched flag is set for products without API details yet
        if 'api_details_fetched' not in metadata:
             metadata['api_details_fetched'] = False

        final_metadata = convert_decimals(flatten_metadata(metadata))
        
        if item_code not in existing_chroma_ids or new_hash != chroma_products_map.get(item_code, {}).get('semantic_hash'):
            ids_to_upsert.append(item_code)
            docs_to_upsert.append(create_rich_document(sql_product, api_details))
            metadatas_to_upsert.append(final_metadata)
        elif final_metadata != chroma_products_map.get(item_code):
            ids_to_update.append(item_code)
            metadatas_to_update.append(final_metadata)

    ids_to_delete = list(existing_chroma_ids - current_sql_ids)

    # 6. Execute all changes in efficient batches
    if ids_to_upsert:
        logging.info(f"Upserting {len(ids_to_upsert)} new or changed products...")
        batch_upsert(collection, ids_to_upsert, docs_to_upsert, metadatas_to_upsert)

    if ids_to_update:
        logging.info(f"Updating metadata for {len(ids_to_update)} products...")
        batch_update(collection, ids_to_update, metadatas_to_update)

    if ids_to_delete:
        logging.info(f"Deleting {len(ids_to_delete)} stale products...")
        batch_delete(collection, ids_to_delete)

    if not any([ids_to_upsert, ids_to_update, ids_to_delete]):
        logging.info("No changes detected. Database is already in sync.")

    logging.info("--- Data sync cycle completed successfully! ---")

# --- MAIN EXECUTION BLOCK (that calls the sync cycle in a loop) ---
if __name__ == "__main__":
    while True:
        try:
            run_data_sync_cycle()
        except Exception as e:
            logging.error(f"An unexpected error occurred during the sync cycle: {e}", exc_info=True)
        
        interval_seconds = REFRESH_INTERVAL_HOURS * 3600
        logging.info(f"Sleeping for {REFRESH_INTERVAL_HOURS} hours ({interval_seconds}) until the next cycle.")
        time.sleep(interval_seconds)