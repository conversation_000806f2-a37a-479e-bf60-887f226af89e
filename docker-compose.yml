services:
  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    platform: linux/amd64
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/data
    environment:
      - CHROMA_SERVER_NO_ANALYTICS=False
      - ALLOW_RESET=false
    networks:
      - recommendation_net

  db:
    image: mysql:8.0
    platform: linux/amd64
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3308:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - recommendation_net

  dataloader:
    build:
      context: ./dataloader
    platform: linux/amd64
    env_file:
      - .env
    depends_on:
      - chroma
      - db
    # We can comment this out after the first successful run if we don't want to reload data every time.
    command: >
      sh -c "
        echo 'Waiting for services...' &&
        sleep 15 && python main.py
      "
    networks:
      - recommendation_net

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    volumes:
      - ./frontend/public:/usr/share/nginx/html
    depends_on:
      backend: # Ensure frontend waits for backend to be healthy
        condition: service_healthy
    networks:
      - recommendation_net

  backend:
    build: ./backend # Ensure ./backend contains a Dockerfile
    ports:
      - "8001:8084"
    volumes:
      - ./backend/app:/app
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - MYSQL_HOST=db
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DATABASE}
      - DB_VIEW_NAME=${DB_VIEW_NAME}

    healthcheck: # Add healthcheck for backend
      test: ["CMD", "curl", "-f", "http://localhost:8084/admin_api/config"] # Check a simple endpoint
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s # Give the app some time to start up
    networks:
      - recommendation_net
    depends_on:
      - dataloader

networks:
  recommendation_net:
    driver: bridge

volumes:
  db_data:
  chroma_data: