<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Display</title>
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
            flex-direction: column;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: white;
        }
        .url-display {
            margin-top: 20px;
            font-size: 1.2em;
            color: #555;
            word-break: break-all;
            text-align: center;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <h1>Scan this QR Code</h1>
    <canvas id="qr-code"></canvas>
    <div class="url-display" id="display-url"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            let urlToEncode = urlParams.get('url');

            // Default URL if no 'url' parameter is provided
            if (!urlToEncode) {
                urlToEncode = 'https://game-morally-lab.ngrok-free.app/'; // Placeholder: Replace with the actual URL you want to convert
            }

            const qr = new QRious({
                element: document.getElementById('qr-code'),
                value: urlToEncode,
                size: 250,
                padding: 10
            });

            document.getElementById('display-url').textContent = `URL: ${urlToEncode}`;
        });
    </script>
</body>
</html>
